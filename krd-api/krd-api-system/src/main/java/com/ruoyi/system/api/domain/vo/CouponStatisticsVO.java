package com.ruoyi.system.api.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 优惠券统计信息
 */
@Data
@Schema(description = "优惠券统计信息")
public class CouponStatisticsVO implements Serializable {
    
    @Schema(description = "总发行量")
    private Integer totalIssued;
    
    @Schema(description = "已领取数量")
    private Integer totalClaimed;
    
    @Schema(description = "待使用数量")
    private Integer pendingUse;
    
    @Schema(description = "已使用数量")
    private Integer totalUsed;
    
    @Schema(description = "优惠券使用率")
    private BigDecimal usageRate;
    
    @Schema(description = "优惠券领取率")
    private BigDecimal claimRate;
}