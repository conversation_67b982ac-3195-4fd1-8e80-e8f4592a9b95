package com.ruoyi.uni.wxpay.strategy.impl;

import com.ruoyi.common.core.constant.PayConstants;
import com.ruoyi.common.core.exception.GlobalException;
import com.ruoyi.system.api.domain.FrontCouponInfo;
import com.ruoyi.system.api.domain.FrontOrders;
import com.ruoyi.system.api.mapper.FrontCouponInfoMapper;
import com.ruoyi.uni.wxpay.response.OrderPayResultResponse;
import com.ruoyi.uni.wxpay.strategy.PaymentStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * @ClassName CouponPayStrategy
 * @Description 优惠券支付策略实现
 * <AUTHOR>
 * @Date 2025/6/26 下午12:04
 */
@Service
@Slf4j
public class CouponPayStrategy  implements PaymentStrategy{

    @Autowired
    private FrontCouponInfoMapper frontCouponInfoMapper;

    @Override
    public OrderPayResultResponse pay(FrontOrders order, String ip) {
        OrderPayResultResponse response = new OrderPayResultResponse();
        response.setOrderNo(order.getOrderNumber());
        response.setPayType(getPayType());

        Long couponInfoId = order.getCouponInfoId();
        // 获取用户优惠券信息
        FrontCouponInfo frontCouponInfo = frontCouponInfoMapper.selectById(couponInfoId);
        if (frontCouponInfo == null) {
            throw new GlobalException("优惠券不存在");
        }

        frontCouponInfo.setStatus("1");
        frontCouponInfoMapper.updateById(frontCouponInfo);

        // 设置订单支付信息
        order.setPaid(true);
        order.setPayTime(LocalDateTime.now());
        order.setGiftDeduction(frontCouponInfo.getBalance());

        return response;
    }

    @Override
    public String getPayType() {
        return PayConstants.PAY_TYPE_COUPON;
    }
}
