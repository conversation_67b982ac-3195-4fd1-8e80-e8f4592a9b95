package com.ruoyi.uni.wxpay.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.PayConstants;
import com.ruoyi.common.core.enums.StoreType;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.JiCeUtil;
import com.ruoyi.common.redis.util.RedisUtil;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.*;
import com.ruoyi.system.api.domain.vo.UniUserVo;
import com.ruoyi.system.api.mapper.*;
import com.ruoyi.uni.erp.PushToErpService;
import com.ruoyi.uni.uni.domain.FrontShopCart;
import com.ruoyi.uni.uni.mapper.FrontShopCartMapper;
import com.ruoyi.uni.uni.mapper.UniFrontGoodsMapper;
import com.ruoyi.uni.uni.service.IFrontOrdersService;
import com.ruoyi.system.api.domain.vo.FrontOrdersVo;
import com.ruoyi.uni.uni.service.IFrontSourceService;
import com.ruoyi.uni.uni.service.IUniUserService;
import com.ruoyi.uni.uni.utils.PdfToImageConverterUtils;
import com.ruoyi.uni.uni.utils.oss.OssUrlCleanerUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 订单总Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Service
public class FrontOrdersServiceImpl extends ServiceImpl<FrontOrdersMapper, FrontOrders> implements IFrontOrdersService
{
    @Autowired
    private FrontOrdersMapper frontOrdersMapper;

    @Autowired
    private UniFrontGoodsMapper uniFrontGoodsMapper;

    @Autowired
    private FrontGoodsSpecMapper frontGoodsSpecMapper;

    @Autowired
    private FrontOrdersGoodsMapper frontOrdersGoodsMapper;

    @Autowired
    private OssUrlCleanerUtil ossUrlCleanerUtil;

    @Autowired
    private FrontEvaluateMapper frontEvaluateMapper;

    @Autowired
    private FrontUserMapper frontUserMapper;

    @Autowired
    private PushToErpService pushToErpService;

    @Autowired
    private FrontShopCartMapper frontShopCartMapper;

    @Autowired
    private FrontFastmailMapper frontFastmailMapper;

    @Autowired
    private IFrontSourceService frontSourceService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private FrontOrdersInvoiceMapper frontOrdersInvoiceMapper;

    @Autowired
    private IUniUserService uniUserService;

    @Autowired
    private SysConfigMapper sysConfigMapper;

    @Autowired
    private FrontGiftInfoMapper frontGiftInfoMapper;

    @Autowired
    private FrontCouponMapper frontCouponMapper;

    @Autowired
    private FrontCouponInfoMapper frontCouponInfoMapper;

    /**
     * 查询订单总
     *
     * @param id 订单总主键
     * @return 订单总
     */
    @Override
    public FrontOrders selectFrontOrdersById(Long id)
    {
        return frontOrdersMapper.selectFrontOrdersById(id);
    }

    /**
     * 查询订单总列表
     *
     * @param vo 查询参数
     * @return 订单总
     */
    @Override
    public List<FrontOrders> selectFrontOrdersList(FrontOrdersVo.FrontOrdersSearch vo)
    {
        return frontOrdersMapper.selectFrontOrdersList(vo);
    }

    @Override
    public List<FrontOrders> selectUniOrdersList(FrontOrdersVo.UniFrontOrdersSearch vo) {
        LambdaQueryWrapper<FrontOrders> eq = new LambdaQueryWrapper<FrontOrders>().eq(FrontOrders::getUserId, SecurityUtils.getUserId()).orderByDesc(FrontOrders::getCreateTime);

        if (vo.getStatus() != -1){
            eq.eq(FrontOrders::getStatus, vo.getStatus());
        }

        List<FrontOrders> frontOrders = frontOrdersMapper.selectList(eq);
        if (frontOrders == null){
            return new ArrayList<>();
        }
        frontOrders.forEach(item -> {
            LambdaQueryWrapper<FrontOrdersGoods> lqw = new LambdaQueryWrapper<FrontOrdersGoods>().eq(FrontOrdersGoods::getOrderId, item.getId());

            // 售后订单拆单显示
            if (vo.getStatus() == 6){
                lqw.in(FrontOrdersGoods::getAfterStatus, 1,2);
            }

            // 通过订单id查询商品信息
            List<FrontOrdersGoods> frontOrdersGoods = frontOrdersGoodsMapper.selectList(lqw);

            List<FrontOrdersVo.FrontOrdersGoodsInfo> frontOrdersGoodsInfoList = new ArrayList<>();

            frontOrdersGoods.forEach(goods -> {
                FrontOrdersVo.FrontOrdersGoodsInfo goodsInfo = new FrontOrdersVo.FrontOrdersGoodsInfo();
                //查询商品信息
                FrontGoods fo = uniFrontGoodsMapper.selectOne(new LambdaQueryWrapper<FrontGoods>().eq(FrontGoods::getId, goods.getGoodsId()));

                goodsInfo.setGoodsId(goods.getGoodsId());
                goodsInfo.setGoodsName(fo.getName());
                if (goods.getGoodsSpecId() != null){
                    goodsInfo.setSpecId(goods.getGoodsSpecId());
                    goodsInfo.setGoodsSpec(frontGoodsSpecMapper.selectOne(new LambdaQueryWrapper<FrontGoodsSpec>().eq(FrontGoodsSpec::getId, goods.getGoodsSpecId())).getTitle());
                }
                goodsInfo.setPayAmount(goods.getPayPrice());
                goodsInfo.setGoodsNum(goods.getCount());
                goodsInfo.setGoodsImg(ossUrlCleanerUtil.getSignatureUrl(fo.getFirstPic()));

                goodsInfo.setIsComment(goods.getCommentStatus());
                goodsInfo.setIsAfter(goods.getAfterStatus());

                frontOrdersGoodsInfoList.add(goodsInfo);

            });
            // 通过订单id查询发票相关信息
            FrontOrdersInvoice invoice = frontOrdersInvoiceMapper.selectOne(new LambdaQueryWrapper<FrontOrdersInvoice>().eq(FrontOrdersInvoice::getOrderId, item.getId()));

            if (invoice != null){
                item.setInvoice(true);
                item.setInvoiceApply(Math.toIntExact(invoice.getStatus()));
            }
            item.setOrderGoods(frontOrdersGoodsInfoList);
        });
        return frontOrders;
    }

    /**
     * 新增订单总
     *
     * @param frontOrders 订单总
     * @return 结果
     */
    @Override
    public int insertFrontOrders(FrontOrders frontOrders)
    {
        frontOrders.setCreateTime(LocalDateTime.now());
        return frontOrdersMapper.insertFrontOrders(frontOrders);
    }

    /**
     * 修改订单总
     *
     * @param frontOrders 订单总
     * @return 结果
     */
    @Override
    public int updateFrontOrders(FrontOrders frontOrders)
    {
        frontOrders.setUpdateTime(LocalDateTime.now());
        return frontOrdersMapper.updateFrontOrders(frontOrders);
    }

    @Override
    public int updateFrontOrdersStatus(FrontOrders frontOrders) {
        LambdaUpdateWrapper<FrontOrders> wrapper = new LambdaUpdateWrapper<FrontOrders>()
               .eq(FrontOrders::getId, frontOrders.getId())
               .set(FrontOrders::getOutTradeNo, frontOrders.getOutTradeNo());
        return frontOrdersMapper.update(null, wrapper);
    }

    /**
     * 批量删除订单总
     *
     * @param ids 需要删除的订单总主键
     * @return 结果
     */
    @Override
    public int deleteFrontOrdersByIds(Long[] ids)
    {
        return frontOrdersMapper.deleteFrontOrdersByIds(ids);
    }

    /**
     * 删除订单总信息
     *
     * @param id 订单总主键
     * @return 结果
     */
    @Override
    public int deleteFrontOrdersById(Long id)
    {
        return frontOrdersMapper.deleteById(id);
    }

    @Override
    public FrontOrders selectOrderNum(String orderNum) {
        return frontOrdersMapper.selectOne(new LambdaQueryWrapper<FrontOrders>().eq(FrontOrders::getOrderNumber, orderNum));
    }

    @Override
    @Transactional
    public String addOrder(FrontOrdersVo.FrontOrdersAddInfo vo) {
        // 获取会员折扣
        BigDecimal discount = frontSourceService.getUserMemberLevelDiscount();
        // 生成订单编号
        String orderNo = JiCeUtil.getNumber(StoreType.STORE.getType());

        List<FrontOrdersGoods> frontOrdersGoodsList = new ArrayList<>();

        // 遍历商品下单信息
        vo.getGoodsInfos().forEach(item -> {
            FrontOrdersGoods frontOrdersGoods = new FrontOrdersGoods();

            // 查询商品/规格数据
            if (item.getSpecId() != null){
                FrontGoodsSpec frontGoodsSpec = frontGoodsSpecMapper.selectById(item.getSpecId());
                frontOrdersGoods.setGoodsId(frontGoodsSpec.getGoodsId());
                frontOrdersGoods.setGoodsSpecId(frontGoodsSpec.getId());
                frontOrdersGoods.setCount(item.getAmount());
                if (discount.compareTo(BigDecimal.ZERO) != 0){
                    frontOrdersGoods.setPrice(frontGoodsSpec.getPrice().multiply(discount).setScale(2, RoundingMode.HALF_UP));
                    frontOrdersGoods.setMeetPrice(frontGoodsSpec.getPrice().multiply(discount).multiply(BigDecimal.valueOf(item.getAmount())).setScale(2, RoundingMode.HALF_UP));
                    frontOrdersGoods.setPayPrice(frontGoodsSpec.getPrice().multiply(discount).multiply(BigDecimal.valueOf(item.getAmount())).setScale(2, RoundingMode.HALF_UP));
                }else {
                    frontOrdersGoods.setPrice(frontGoodsSpec.getPrice());
                    frontOrdersGoods.setMeetPrice(frontGoodsSpec.getPrice().multiply(BigDecimal.valueOf(item.getAmount())));
                    frontOrdersGoods.setPayPrice(frontGoodsSpec.getPrice().multiply(BigDecimal.valueOf(item.getAmount())));
                }
            }else {
                FrontGoods goods = uniFrontGoodsMapper.selectById(item.getGoodsId());
                frontOrdersGoods.setGoodsId(goods.getId());
                frontOrdersGoods.setCount(item.getAmount());
                if (discount.compareTo(BigDecimal.ZERO) != 0){
                    frontOrdersGoods.setPrice(goods.getPrice().multiply(discount).setScale(2, RoundingMode.HALF_UP));
                    frontOrdersGoods.setMeetPrice(goods.getPrice().multiply(discount).multiply(BigDecimal.valueOf(item.getAmount())).setScale(2, RoundingMode.HALF_UP));
                    frontOrdersGoods.setPayPrice(goods.getPrice().multiply(discount).multiply(BigDecimal.valueOf(item.getAmount())).setScale(2, RoundingMode.HALF_UP));
                }else {
                    frontOrdersGoods.setPrice(goods.getPrice());
                    frontOrdersGoods.setMeetPrice(goods.getPrice().multiply(BigDecimal.valueOf(item.getAmount())));
                    frontOrdersGoods.setPayPrice(goods.getPrice().multiply(BigDecimal.valueOf(item.getAmount())));
                }
            }
            frontOrdersGoods.setType(item.getGoodsType());
            frontOrdersGoodsList.add(frontOrdersGoods);

            // 下单 之后删除购物车
            LambdaQueryWrapper<FrontShopCart> queryWrapper = new LambdaQueryWrapper<FrontShopCart>()
                    .eq(FrontShopCart::getGoodsId, item.getGoodsId())
                    .eq(FrontShopCart::getUserId, SecurityUtils.getUserId());

            if (item.getSpecId() != null){
                queryWrapper.eq(FrontShopCart::getSpecId, item.getSpecId());
            }else {
                queryWrapper.isNull(FrontShopCart::getSpecId);
            }
            frontShopCartMapper.delete(queryWrapper);
        });

        // 订单信息
        FrontOrders frontOrders = new FrontOrders();
        frontOrders.setOrderNumber(orderNo);
        frontOrders.setUserId(SecurityUtils.getUserId());
        frontOrders.setTotalPrice(frontOrdersGoodsList.stream()
                .map(FrontOrdersGoods::getMeetPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        frontOrders.setPayPrice(frontOrdersGoodsList.stream()
                .map(FrontOrdersGoods::getPayPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        frontOrders.setCreateTime(LocalDateTime.now());
        frontOrders.setIsAfter(0);
        frontOrders.setPayType("weixin");
        frontOrders.setStatus(0);
        frontOrders.setPaid(false);
        if (vo.getAddressId() != null){
            frontOrders.setAddressId(vo.getAddressId());
        }
        if (vo.getReceiveName() != null){
            frontOrders.setReceiveName(vo.getReceiveName());
        }
        if (vo.getReceivePhone() != null){
            frontOrders.setReceivePhone(vo.getReceivePhone());
        }
        if (vo.getReceiveAddress() != null){
            frontOrders.setReceiveAddress(vo.getReceiveAddress());
        }
        if (vo.getReceiveAddressDetail() != null){
            frontOrders.setReceiveAddressDetail(vo.getReceiveAddressDetail());
        }
        frontOrders.setIsDel(false);
        frontOrdersMapper.insert(frontOrders);

        // 保存订单id 入库
        frontOrdersGoodsList.forEach(item -> {
            item.setOrderId(frontOrders.getId());
            frontOrdersGoodsMapper.insert(item);
        });

//        pushToErpService.pushOrderToErp(frontOrders,frontOrdersGoodsList);
        // 添加订单自动取消任务
        CancelOrder cancelOrder = new CancelOrder();
        cancelOrder.setOrderNumber(orderNo);
        redisUtil.lPush(PayConstants.ORDER_AUTO_CANCEL_KEY, cancelOrder);
        return orderNo;
    }

    @Override
    public FrontOrdersVo.FrontOrdersAddResult selectOrderDetailByOrderNo(String orderNo) {
        // 查询订单信息
        FrontOrders frontOrders = frontOrdersMapper.selectOne(new LambdaQueryWrapper<FrontOrders>().eq(FrontOrders::getOrderNumber, orderNo));

        FrontOrdersVo.FrontOrdersAddResult frontOrdersAddResult = new FrontOrdersVo.FrontOrdersAddResult();

        frontOrdersAddResult.setOrderNo(frontOrders.getOrderNumber());
        frontOrdersAddResult.setAddressId(frontOrders.getAddressId());
        frontOrdersAddResult.setReceiveName(frontOrders.getReceiveName());
        frontOrdersAddResult.setReceivePhone(frontOrders.getReceivePhone());
        frontOrdersAddResult.setReceiveAddress(frontOrders.getReceiveAddress());
        frontOrdersAddResult.setReceiveAddressDetail(frontOrders.getReceiveAddressDetail());
        frontOrdersAddResult.setTotalPrice(frontOrders.getTotalPrice());
        frontOrdersAddResult.setPayPrice(frontOrders.getPayPrice());
        frontOrdersAddResult.setStatus(frontOrders.getStatus());
        frontOrdersAddResult.setCreateTime(frontOrders.getCreateTime());
        frontOrdersAddResult.setPayType(frontOrders.getPayType());
        frontOrdersAddResult.setPayTime(frontOrders.getPayTime());
        frontOrdersAddResult.setFinishTime(frontOrders.getFinishTime());

        // 查询商品信息
        List<FrontOrdersGoods> frontOrdersGoods = frontOrdersGoodsMapper.selectList(new LambdaQueryWrapper<FrontOrdersGoods>().eq(FrontOrdersGoods::getOrderId, frontOrders.getId()));

        List<FrontOrdersVo.OrdersGoodsInfo> list = new ArrayList<>();

        frontOrdersGoods.forEach(item ->{
            FrontOrdersVo.OrdersGoodsInfo ordersGoodsInfo = new FrontOrdersVo.OrdersGoodsInfo();
            FrontGoods goods = uniFrontGoodsMapper.selectOne(new LambdaQueryWrapper<FrontGoods>().eq(FrontGoods::getId, item.getGoodsId()));
            ordersGoodsInfo.setGoodsId(item.getGoodsId());
            ordersGoodsInfo.setGoodsName(goods.getName());
            if (item.getGoodsSpecId() != null){
                ordersGoodsInfo.setSpecId(item.getGoodsSpecId());
                ordersGoodsInfo.setSpecName(frontGoodsSpecMapper.selectOne(new LambdaQueryWrapper<FrontGoodsSpec>().eq(FrontGoodsSpec::getId, item.getGoodsSpecId())).getTitle());
            }
            ordersGoodsInfo.setGoodsImg(ossUrlCleanerUtil.getSignatureUrl(goods.getFirstPic()));
            ordersGoodsInfo.setGoodsNum(item.getCount());
            ordersGoodsInfo.setGoodsPrice(item.getPrice());
            ordersGoodsInfo.setGoodsDiscountPrice(item.getMeetPrice());

            ordersGoodsInfo.setIsComment(item.getCommentStatus());
            ordersGoodsInfo.setIsAfter(item.getAfterStatus());
            ordersGoodsInfo.setRefundType(item.getRefundType());

            list.add(ordersGoodsInfo);
        });

        // 通过订单号查询快递信息
        Long count = frontFastmailMapper.selectCount(new LambdaQueryWrapper<FrontFastmail>().eq(FrontFastmail::getOrderNumber, orderNo).eq(FrontFastmail::getFastType, 1));

        if (count > 0){
            frontOrdersAddResult.setIsWriteExpress(true);
        }

        frontOrdersAddResult.setOrderGoodsInfos(list);

        return frontOrdersAddResult;
    }

    @Override
    public Boolean cancelOrder(String orderNo) {
        FrontOrders frontOrders = getFrontOrders(orderNo);
        frontOrders.setStatus(5);
        return frontOrdersMapper.updateById(frontOrders) > 0;
    }

    @Override
    public Boolean updateAddress(FrontOrdersVo.UpdateAddressInfo vo) {
        FrontOrders frontOrders = frontOrdersMapper.selectOne(new LambdaQueryWrapper<FrontOrders>().eq(FrontOrders::getOrderNumber, vo.getOrderNo()));
        if (frontOrders == null){
            throw new RuntimeException("订单不存在");
        }
        frontOrders.setAddressId(vo.getAddressId());
        frontOrders.setReceiveName(vo.getReceiveName());
        frontOrders.setReceivePhone(vo.getReceivePhone());
        frontOrders.setReceiveAddress(vo.getReceiveAddress());
        frontOrders.setReceiveAddressDetail(vo.getReceiveAddressDetail());
        return frontOrdersMapper.updateById(frontOrders) > 0;
    }

    @Override
    public Boolean finishOrder(String orderNo) {
        FrontOrders frontOrders = getFrontOrders(orderNo);
        frontOrders.setFinishTime(LocalDateTime.now());
        frontOrders.setStatus(4);
        return frontOrdersMapper.updateById(frontOrders) > 0;
    }

    @Override
    @Transactional
    public Boolean comment(FrontOrdersVo.CommentInfo vo) {
        FrontOrders frontOrders = getFrontOrders(vo.getOrderNo());
        FrontEvaluate frontEvaluate = new FrontEvaluate();
        frontEvaluate.setOrderId(frontOrders.getId());
        frontEvaluate.setGoodsId(vo.getGoodsId());
        frontEvaluate.setSpecId(vo.getSpecId());
        frontEvaluate.setUserId(SecurityUtils.getUserId());
        frontEvaluate.setStarRating(vo.getStarRating());
        frontEvaluate.setType(vo.getType());
        frontEvaluate.setContent(vo.getContent());
        frontEvaluate.setPic(ossUrlCleanerUtil.cleanUrlsToString(vo.getPic()));
        frontEvaluate.setTickTime(LocalDateTime.now());

        // 查询订单商品信息
        LambdaQueryWrapper<FrontOrdersGoods> lqw = new LambdaQueryWrapper<FrontOrdersGoods>()
                .eq(FrontOrdersGoods::getOrderId, frontOrders.getId())
                .eq(FrontOrdersGoods::getGoodsId, vo.getGoodsId());

        if (vo.getSpecId() != 0){
            lqw.eq(FrontOrdersGoods::getGoodsSpecId, vo.getSpecId());
        }else {
            lqw.isNull(FrontOrdersGoods::getGoodsSpecId);
        }

        FrontOrdersGoods frontOrdersGoods = frontOrdersGoodsMapper.selectOne(lqw);

        if (frontOrdersGoods != null){
            frontOrdersGoods.setCommentStatus(true);
            frontOrdersGoodsMapper.updateById(frontOrdersGoods);
        }

        //当前订单下所有商品都评论了
        LambdaQueryWrapper<FrontOrdersGoods> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(FrontOrdersGoods::getOrderId, frontOrders.getId());
        lambdaQueryWrapper.eq(FrontOrdersGoods::getCommentStatus, false);
        if (frontOrdersGoodsMapper.selectCount(lambdaQueryWrapper) == 0){
            frontOrders.setStatus(3);
            frontOrdersMapper.updateById(frontOrders);
        }

        return frontEvaluateMapper.insert(frontEvaluate) > 0;
    }

    @Override
    @Transactional
    public Boolean payment(FrontOrdersVo.UpdateAddressInfo vo) {
        FrontOrders frontOrders = getFrontOrders(vo.getOrderNo());
        frontOrders.setAddressId(vo.getAddressId());
        frontOrders.setReceiveName(vo.getReceiveName());
        frontOrders.setPayTime(LocalDateTime.now());
        frontOrders.setReceivePhone(vo.getReceivePhone());
        frontOrders.setReceiveAddress(vo.getReceiveAddress());
        frontOrders.setReceiveAddressDetail(vo.getReceiveAddressDetail());
        frontOrders.setPaid(true);
        frontOrders.setStatus(1);
        frontOrders.setUpdateTime(LocalDateTime.now());
        frontOrdersMapper.updateById(frontOrders);

        try {
            pushToErpService.pushOrderToErp(frontOrders);

            return Boolean.TRUE;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional
    public String refundOrder(FrontOrdersVo.RefundInfo vo) {
        FrontOrders frontOrders = getFrontOrders(vo.getOrderNo());
        // 订单退款 判断 是否有商品和规格
        if (vo.getRefundOrderGoodsInfoList() == null){
            // 通过订单id查询订单商品信息
            List<FrontOrdersGoods> frontOrdersGoodsList = frontOrdersGoodsMapper.selectList(new LambdaQueryWrapper<FrontOrdersGoods>().eq(FrontOrdersGoods::getOrderId, frontOrders.getId()));
            frontOrdersGoodsList.forEach(item ->{
                FrontOrdersGoods frontOrdersGoods = getFrontOrdersGoods(frontOrders.getId(), item.getGoodsId(), item.getGoodsSpecId());
                frontOrdersGoods.setAfterStatus(1);
                frontOrdersGoods.setAfterReason(vo.getReason());
                frontOrdersGoods.setAfterDesc(vo.getDesc());
                frontOrdersGoods.setApplyTime(LocalDateTime.now());
                frontOrdersGoods.setAfterPrice(frontOrdersGoods.getPayPrice());
                frontOrdersGoods.setRefundImg(ossUrlCleanerUtil.cleanUrlsToString(vo.getPic()));
                frontOrdersGoods.setRefundType(vo.getType());
                frontOrdersGoodsMapper.updateById(frontOrdersGoods);
            });
            frontOrders.setStatus(6);
            frontOrdersMapper.updateById(frontOrders);
//            try {
//                pushToErpService.pushRefundToErp(frontOrders,frontOrdersGoodsList);
//            } catch (Exception e) {
//                throw new RuntimeException(e);
//            }
            return vo.getOrderNo();
        }else {
            // 将frontOrdersGoods转为list
            List<FrontOrdersGoods> list = new ArrayList<>();
            String refundNo = frontOrders.getOrderNumber() + "—" + JiCeUtil.randomCount(111,999);
            vo.getRefundOrderGoodsInfoList().forEach(item -> {
                FrontOrdersGoods frontOrdersGoods = getFrontOrdersGoodsAfter(frontOrders.getId(), item.getGoodsId(), item.getSpecId(),0);

                // 判断当前退货数量是否等于订单商品数量
                if (item.getGoodsNum().equals(frontOrdersGoods.getCount())){

                    frontOrdersGoods.setAfterStatus(1);
                    frontOrdersGoods.setAfterReason(vo.getReason());
                    frontOrdersGoods.setAfterDesc(vo.getDesc());
                    frontOrdersGoods.setApplyTime(LocalDateTime.now());
                    frontOrdersGoods.setAfterPrice(frontOrdersGoods.getPayPrice());
                    frontOrdersGoods.setRefundImg(ossUrlCleanerUtil.cleanUrlsToString(vo.getPic()));
                    frontOrdersGoods.setRefundType(vo.getType());

                    list.add(frontOrdersGoods);

                    frontOrdersGoodsMapper.deleteById(frontOrdersGoods);
                }else {
                    FrontOrdersGoods orderGoods = new FrontOrdersGoods();
                    BeanUtils.copyProperties(frontOrdersGoods,orderGoods);
                    orderGoods.setId(null);
                    orderGoods.setAfterStatus(1);
                    orderGoods.setAfterReason(vo.getReason());
                    orderGoods.setAfterDesc(vo.getDesc());
                    orderGoods.setApplyTime(LocalDateTime.now());
                    orderGoods.setRefundImg(ossUrlCleanerUtil.cleanUrlsToString(vo.getPic()));
                    orderGoods.setRefundType(vo.getType());
                    orderGoods.setCount(item.getGoodsNum());
                    BigDecimal refundPrice = frontOrdersGoods.getPrice().multiply(BigDecimal.valueOf(item.getGoodsNum()));

                    // todo 这里需要处理优惠券 礼品卡 等 后续根据 组合支付 进行完善 拆单
                    orderGoods.setAckAfterPrice(refundPrice);
                    orderGoods.setAfterPrice(refundPrice);
                    orderGoods.setMeetPrice(refundPrice);
                    orderGoods.setPayPrice(refundPrice);
                    frontOrdersGoods.setCount(frontOrdersGoods.getCount()-item.getGoodsNum());
                    frontOrdersGoods.setMeetPrice(frontOrdersGoods.getMeetPrice().subtract(refundPrice));
                    frontOrdersGoods.setPayPrice(frontOrdersGoods.getPayPrice().subtract(refundPrice));

                    list.add(orderGoods);

                    frontOrdersGoodsMapper.updateById(frontOrdersGoods);
                }

            });

            FrontOrders orders = new FrontOrders();
            orders.setUserId(SecurityUtils.getUserId());
            orders.setTotalPrice(list.stream()
                    .map(FrontOrdersGoods::getMeetPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            orders.setPayPrice(list.stream()
                    .map(FrontOrdersGoods::getPayPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            orders.setCreateTime(LocalDateTime.now());
            orders.setIsAfter(0);
            orders.setPayType(frontOrders.getPayType());
            orders.setStatus(6);
            orders.setPaid(frontOrders.getPaid());
            orders.setOrderNumber(refundNo);
            orders.setReceiveName(frontOrders.getReceiveName());
            orders.setReceivePhone(frontOrders.getReceivePhone());
            orders.setReceiveAddress(frontOrders.getReceiveAddress());
            orders.setReceiveAddressDetail(frontOrders.getReceiveAddressDetail());
            orders.setIsDel(false);
            frontOrdersMapper.insert(orders);

            list.forEach(item ->{
                item.setOrderId(orders.getId());
                frontOrdersGoodsMapper.insert( item);
            });

            //
//            try {
//                pushToErpService.pushRefundToErp(frontOrders,list);
//            } catch (Exception e) {
//                throw new RuntimeException(e);
//            }

            return refundNo;
        }

    }

    @Override
    public FrontOrdersVo.RefundReturnInfo getAfterInfo(FrontOrdersVo.RefundList vo) {
        FrontOrders frontOrders = getFrontOrders(vo.getOrderNo());

        // 查询用户信息
        FrontUser frontUser = frontUserMapper.selectFrontUserById(frontOrders.getUserId());

        // 保存数据
        FrontOrdersVo.RefundReturnInfo refundReturnInfo = new FrontOrdersVo.RefundReturnInfo();
        refundReturnInfo.setRefundName(frontUser.getUserName());
        refundReturnInfo.setRefundPhone(frontUser.getUserMobile());

        List<FrontOrdersVo.RefundGoodsInfo> refundGoodsInfoList = new ArrayList<>();

        // 通过订单id查询订单对应商品信息
        List<FrontOrdersGoods> frontOrdersGoodsList = frontOrdersGoodsMapper.selectList(new LambdaQueryWrapper<FrontOrdersGoods>().eq(FrontOrdersGoods::getOrderId, frontOrders.getId()).eq(FrontOrdersGoods::getAfterStatus, 0));

        vo.getRefundList().forEach(item -> {
            FrontOrdersGoods frontOrdersGoods = getFrontOrdersGoodsAfter(frontOrders.getId(), item.getGoodsId(), item.getSpecId(),item.getIsAfter());

            FrontOrdersVo.RefundGoodsInfo goodsInfo = getGoodsInfo(frontOrdersGoods);

            refundGoodsInfoList.add(goodsInfo);
        });

        // 实际退款金额等于所有商品金额相加
        refundReturnInfo.setRefundPrice(refundGoodsInfoList.stream()
                .map(FrontOrdersVo.RefundGoodsInfo::getGoodsTotalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add));

        refundReturnInfo.setRefundGoodsInfos(refundGoodsInfoList);

        return refundReturnInfo;
    }

    @Override
    public FrontOrdersVo.CommentInfo getComment(FrontOrdersVo.CommentInfo vo) {
        FrontOrders frontOrders = getFrontOrders(vo.getOrderNo());
        // 查询评价详情
        LambdaQueryWrapper<FrontEvaluate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FrontEvaluate::getOrderId, frontOrders.getId());
        queryWrapper.eq(FrontEvaluate::getGoodsId, vo.getGoodsId());

        if (vo.getSpecId() != 0){
            queryWrapper.eq(FrontEvaluate::getSpecId, vo.getSpecId());
        }else {
            queryWrapper.isNull(FrontEvaluate::getSpecId);
        }
        FrontEvaluate frontEvaluate = frontEvaluateMapper.selectOne(queryWrapper);
        if (frontEvaluate != null){
            FrontOrdersVo.CommentInfo commentInfo = new FrontOrdersVo.CommentInfo();
            commentInfo.setContent(frontEvaluate.getContent());
            commentInfo.setPic(ossUrlCleanerUtil.getSignatureUrl(frontEvaluate.getPic()));
            commentInfo.setStarRating(frontEvaluate.getStarRating());
            return commentInfo;
        }
         return null;
    }

    @Override
    public FrontOrdersVo.RefundDetailInfo getRefundInfo(String orderNo) {
        FrontOrders frontOrders = getFrontOrders(orderNo);

        // 保存数据
        FrontOrdersVo.RefundDetailInfo refundDetailInfo = new FrontOrdersVo.RefundDetailInfo();
        refundDetailInfo.setOrderNo(frontOrders.getOrderNumber());

        List<FrontOrdersVo.RefundGoodsInfo> refundGoodsInfoList = new ArrayList<>();

        // 通过订单id查询订单对应商品信息
        List<FrontOrdersGoods> frontOrdersGoodsList = frontOrdersGoodsMapper.selectList(new LambdaQueryWrapper<FrontOrdersGoods>().eq(FrontOrdersGoods::getOrderId, frontOrders.getId()));
        frontOrdersGoodsList.forEach(item -> {

            FrontOrdersVo.RefundGoodsInfo goodsInfo = getGoodsInfo(item);
            refundDetailInfo.setRefundStatus(item.getAfterStatus());
            refundGoodsInfoList.add(goodsInfo);
            refundDetailInfo.setPic(ossUrlCleanerUtil.getSignatureUrl(item.getRefundImg()));
            refundDetailInfo.setApplyTime(item.getApplyTime());
            refundDetailInfo.setReason(item.getAfterReason());

        });

        refundDetailInfo.setRefundGoodsInfos(refundGoodsInfoList);

        refundDetailInfo.setRefundPrice(refundGoodsInfoList.stream()
                .map(FrontOrdersVo.RefundGoodsInfo::getGoodsTotalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add));

        return refundDetailInfo;
    }

    @Override
    @Transactional
    public Boolean cancelRefundOrder(String orderNo) {
        FrontOrders frontOrders = getFrontOrders(orderNo);

        LambdaQueryWrapper<FrontOrdersGoods> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FrontOrdersGoods::getOrderId, frontOrders.getId());

        // 查询订单全部对应商品
        List<FrontOrdersGoods> ordersGoodsList = frontOrdersGoodsMapper.selectList(queryWrapper);

        // 查询售后订单对应商品
        List<FrontOrdersGoods> refundOrdersGoodsList = frontOrdersGoodsMapper.selectList(queryWrapper.eq(FrontOrdersGoods::getAfterStatus, 1));

        refundOrdersGoodsList.forEach(item -> {
            item.setAfterStatus(0);
            item.setAfterPrice(null);
            item.setAckAfterPrice(null);
            item.setAfterReason(null);
            item.setAfterDesc(null);
            item.setAfterAddress(null);
            item.setUpdateTime(DateUtils.getLocalDateTime());
            frontOrdersGoodsMapper.updateById(item);
        });

        if (ordersGoodsList.size() == refundOrdersGoodsList.size()){
            frontOrders.setStatus(2);
            frontOrdersMapper.updateById(frontOrders);
        }
        return true;
    }

    @Override
    public Boolean updateRefundOrder(FrontOrdersVo.ReturnExpressInfo vo) {
        FrontOrders frontOrders = getFrontOrders(vo.getOrderNo());
        FrontFastmail frontFastmail = new FrontFastmail();

        frontFastmail.setOrderNumber(vo.getOrderNo());
        frontFastmail.setFastType(1L);
        frontFastmail.setCompany(vo.getExpressCompany());
        frontFastmail.setNumber(vo.getExpressNo());
        frontFastmail.setOrderTime(frontOrders.getCreateTime());

        // 获取用户信息
        FrontUser frontUser = frontUserMapper.selectById(frontOrders.getUserId());
        frontFastmail.setUserMobile(frontUser.getUserMobile());
        frontFastmailMapper.insert(frontFastmail);

        return true;
    }

    @Override
    public Boolean deleteOrder(String orderNo) {
        LambdaQueryWrapper<FrontOrders> queryWrapper = new LambdaQueryWrapper<FrontOrders>()
                .eq(FrontOrders::getOrderNumber, orderNo);
        return frontOrdersMapper.delete(queryWrapper) > 0;
    }

    @Override
    public Boolean invoiceApply(FrontOrdersInvoice invoice) {
        invoice.setCreateTime(LocalDateTime.now());
        invoice.setStatus(0L);
        invoice.setUserId(uniUserService.getUserId());
        return frontOrdersInvoiceMapper.insert(invoice) > 0;
    }

    @Override
    public FrontOrdersInvoice selectInvoiceByUserId() {
        // 查询用户发票最新信息
        LambdaQueryWrapper<FrontOrdersInvoice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FrontOrdersInvoice::getUserId, uniUserService.getUserId());
        queryWrapper.orderByDesc(FrontOrdersInvoice::getCreateTime);
        queryWrapper.last("limit 1");
        return frontOrdersInvoiceMapper.selectOne(queryWrapper);
    }

    @Override
    @Transactional
    public String againOrder(String orderNo) {
        // 查询订单信息
        FrontOrders frontOrders = getFrontOrders(orderNo);
        FrontOrdersVo.FrontOrdersAddInfo frontOrdersAddInfo = new FrontOrdersVo.FrontOrdersAddInfo();
        frontOrdersAddInfo.setReceiveName(frontOrders.getReceiveName());
        frontOrdersAddInfo.setReceivePhone(frontOrders.getReceivePhone());
        frontOrdersAddInfo.setReceiveAddress(frontOrders.getReceiveAddress());
        frontOrdersAddInfo.setReceiveAddressDetail(frontOrders.getReceiveAddressDetail());
        // 查询订单商品信息
        List<FrontOrdersGoods> frontOrdersGoodsList = frontOrdersGoodsMapper.selectList(new LambdaQueryWrapper<FrontOrdersGoods>().eq(FrontOrdersGoods::getOrderId, frontOrders.getId()));
        List<FrontOrdersVo.FrontOrdersGoodsAddInfo> frontOrdersGoodsAddInfoList = new ArrayList<>();
        frontOrdersGoodsList.forEach(item -> {
            FrontOrdersVo.FrontOrdersGoodsAddInfo frontOrdersGoodsAddInfo = new FrontOrdersVo.FrontOrdersGoodsAddInfo();
            frontOrdersGoodsAddInfo.setGoodsId(item.getGoodsId());
            frontOrdersGoodsAddInfo.setSpecId(item.getGoodsSpecId());
            frontOrdersGoodsAddInfo.setGoodsType(item.getType());
            frontOrdersGoodsAddInfo.setAmount(item.getCount());
            frontOrdersGoodsAddInfoList.add(frontOrdersGoodsAddInfo);
        });
        frontOrdersAddInfo.setGoodsInfos(frontOrdersGoodsAddInfoList);
        return addOrder(frontOrdersAddInfo);
    }

    @Override
    public FrontOrdersVo.InvoiceInfo selectInvoiceByOrderId(Long orderId) {
        FrontOrdersInvoice frontOrdersInvoice = frontOrdersInvoiceMapper.selectOne(new LambdaQueryWrapper<FrontOrdersInvoice>().eq(FrontOrdersInvoice::getOrderId, orderId));
        if (frontOrdersInvoice != null){
            FrontOrdersVo.InvoiceInfo invoiceInfo = new FrontOrdersVo.InvoiceInfo();
            invoiceInfo.setPdf(ossUrlCleanerUtil.getSignatureUrl(frontOrdersInvoice.getUrl()));
            invoiceInfo.setPic(PdfToImageConverterUtils.convertPdfToImages(invoiceInfo.getPdf(), 300, "png"));
            return invoiceInfo;
        }
        return null;
    }

    @Override
    public FrontOrdersVo.OrderPayRequest getOrderPayRequest() {
        // 对象保存
        FrontOrdersVo.OrderPayRequest request = new FrontOrdersVo.OrderPayRequest();
        // 获取用户信息
        FrontUser userInfo = uniUserService.getUserInfo();
        // 获取积分系统配置
        SysConfig sysConfig = sysConfigMapper.checkConfigKeyUnique("points_config");
        String configValue = sysConfig.getConfigValue();
        Map<String, Object> pointsConfig = JSONObject.parseObject(configValue);
        BigDecimal pointsExchange = new BigDecimal(pointsConfig.get("pointsExchange").toString());
        BigDecimal pointsExchangeAmount = new BigDecimal(pointsConfig.get("pointsExchangeAmount").toString());

        request.setBalance(userInfo.getNowMoney());
        request.setPoints(userInfo.getIntegral());
        request.setPointsDeduction(pointsExchange);
        request.setPointsPrice(pointsExchangeAmount);
        request.setGiftCard(frontGiftInfoMapper.selectList(new LambdaQueryWrapper<FrontGiftInfo>().in(FrontGiftInfo::getStatus, "0","1","3").eq(FrontGiftInfo::getUserId, userInfo.getId())));
        List<FrontCouponInfo> frontCouponInfos = frontCouponInfoMapper.selectList(new LambdaQueryWrapper<FrontCouponInfo>().eq(FrontCouponInfo::getStatus, "0").eq(FrontCouponInfo::getUserId, userInfo.getId()));
        List<UniUserVo.UserCouponList> userCouponLists = new ArrayList<>();
        if (frontCouponInfos != null){
            frontCouponInfos.forEach(item ->{
                UniUserVo.UserCouponList userCouponList = new UniUserVo.UserCouponList();
                // 通过id查询优惠券信息
                StoreFrontCoupon frontCoupon = frontCouponMapper.selectById(item.getCouponId());
                userCouponList.setId(item.getId());
                userCouponList.setName(frontCoupon.getTitle());
                userCouponList.setThreshold(frontCoupon.getThreshold());
                userCouponList.setBalance(frontCoupon.getBalance());
                userCouponList.setExpireTime(String.valueOf(item.getUseTime()));
                userCouponLists.add(userCouponList);
            });
        }
        request.setCoupon(userCouponLists);

        return request;
    }

    private FrontOrders getFrontOrders(String orderNo){
        FrontOrders frontOrders = frontOrdersMapper.selectOne(new LambdaQueryWrapper<FrontOrders>().eq(FrontOrders::getOrderNumber, orderNo));
        if (frontOrders == null){
            throw new RuntimeException("订单不存在");
        }
        return frontOrders;
    }

    private FrontOrdersGoods getFrontOrdersGoods(Long orderId, Long goodsId, Long specId){
        LambdaQueryWrapper<FrontOrdersGoods> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FrontOrdersGoods::getOrderId, orderId);
        queryWrapper.eq(FrontOrdersGoods::getGoodsId, goodsId);
        if (specId != null){
            queryWrapper.eq(FrontOrdersGoods::getGoodsSpecId, specId);
        }else {
            queryWrapper.isNull(FrontOrdersGoods::getGoodsSpecId);
        }
        FrontOrdersGoods frontOrdersGoods = frontOrdersGoodsMapper.selectOne(queryWrapper);
        if (frontOrdersGoods == null){
            throw new RuntimeException("订单对应商品不存在");
        }
        return frontOrdersGoods;
    }

    private FrontOrdersGoods getFrontOrdersGoodsAfter(Long orderId, Long goodsId, Long specId, Integer afterStatus){
        LambdaQueryWrapper<FrontOrdersGoods> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FrontOrdersGoods::getOrderId, orderId);
        queryWrapper.eq(FrontOrdersGoods::getGoodsId, goodsId);

        if (afterStatus != null){
            queryWrapper.eq(FrontOrdersGoods::getAfterStatus, afterStatus);
        }

        if (specId != null){
            queryWrapper.eq(FrontOrdersGoods::getGoodsSpecId, specId);
        }else {
            queryWrapper.isNull(FrontOrdersGoods::getGoodsSpecId);
        }
        FrontOrdersGoods frontOrdersGoods = frontOrdersGoodsMapper.selectOne(queryWrapper);
        if (frontOrdersGoods == null){
            throw new RuntimeException("订单对应商品不存在");
        }
        return frontOrdersGoods;
    }

    /**
     * 查询商品信息
     */
    private FrontOrdersVo.RefundGoodsInfo getGoodsInfo(FrontOrdersGoods frontOrdersGoods){
        // 获取会员折扣
        BigDecimal discount = frontSourceService.getUserMemberLevelDiscount();
        // 查询商品信息
        FrontGoods uniFrontGoods = uniFrontGoodsMapper.selectOne(new LambdaQueryWrapper<FrontGoods>().eq(FrontGoods::getId, frontOrdersGoods.getGoodsId()));

        // 保存商品信息
        FrontOrdersVo.RefundGoodsInfo refundGoodsInfo = new FrontOrdersVo.RefundGoodsInfo();
        refundGoodsInfo.setGoodsId(frontOrdersGoods.getGoodsId());
        refundGoodsInfo.setGoodsName(uniFrontGoods.getName());
        refundGoodsInfo.setGoodsPrice(frontOrdersGoods.getPrice());
        if (discount.compareTo(BigDecimal.ZERO) != 0){
            refundGoodsInfo.setGoodsDiscountPrice(frontOrdersGoods.getPrice().multiply(discount).setScale(2, RoundingMode.HALF_UP));
        }else {
            refundGoodsInfo.setGoodsDiscountPrice(frontOrdersGoods.getPrice());
        }
        refundGoodsInfo.setGoodsNum(frontOrdersGoods.getCount());
        refundGoodsInfo.setGoodsTotalPrice(refundGoodsInfo.getGoodsDiscountPrice().multiply(new BigDecimal(frontOrdersGoods.getCount())).setScale(2, RoundingMode.HALF_UP));
        if (uniFrontGoods.getFirstPic() != null){
            refundGoodsInfo.setGoodsImg(ossUrlCleanerUtil.getSignatureUrl(uniFrontGoods.getFirstPic()));
        }
        if (frontOrdersGoods.getGoodsSpecId() != null){
            refundGoodsInfo.setSpecId(frontOrdersGoods.getGoodsSpecId());
            FrontGoodsSpec frontGoodsSpec = frontGoodsSpecMapper.selectOne(new LambdaQueryWrapper<FrontGoodsSpec>().eq(FrontGoodsSpec::getId, frontOrdersGoods.getGoodsSpecId()));
            refundGoodsInfo.setSpecName(frontGoodsSpec.getTitle());
        }
        return refundGoodsInfo;
    }
}
