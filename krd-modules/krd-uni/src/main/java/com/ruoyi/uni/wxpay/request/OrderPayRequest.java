package com.ruoyi.uni.wxpay.request;

import com.ruoyi.system.api.domain.vo.GiftDeduVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: suhai
 * @Date: 2025/5/23
 * @Description: 小程序订单支付参数
 */

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class OrderPayRequest {
    @Schema(title = "订单id")
//    @NotNull(message = "订单id不能为空")
    private String uni;

    @Schema(title = "订单编号")
    @NotNull(message = "订单编号不能为空")
    private String orderNo;

    @Schema(title = "支付类型：weixin-微信支付，yue-余额支付，offline-线下支付，alipay-支付包支付")
    @NotNull(message = "支付类型不能为空")
    private String payType;

    @Schema(title = "支付渠道:weixinh5-微信H5支付，public-公众号支付，routine-小程序支付，weixinAppIos-微信appios支付，weixinAppAndroid-微信app安卓支付,alipay-支付包支付，appAliPay-App支付宝支付")
    private String payChannel;

    @Schema(title = "支付平台")
    private String from;

    @Schema(title = "下单时小程序的场景值")
    private Integer scene;

    @Schema(title = "礼品卡抵扣列表")
    private List<GiftDeduVo> giftList;

    @Schema(title = "用户优惠券明细id")
    private Long couponInfoId;

    @Schema(title = "用户备注")
    private String remark;

    @Schema(title = "收货人")
    private String receiveName;
    @Schema(title = "收货人手机号")
    private String receivePhone;
    @Schema(title = "收货人地址")
    private String receiveAddress;
    @Schema(title = "收货人地址详情")
    private String receiveAddressDetail;

}
