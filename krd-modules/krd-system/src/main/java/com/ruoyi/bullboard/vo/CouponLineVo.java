package com.ruoyi.bullboard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 优惠券统计信息
 */
@Data
@Schema(description = "优惠券统计信息")
public class CouponLineVo implements Serializable {

    //时间
    private String date;

    //待使用
    private Integer pendingUse;

    //已使用
    private Integer totalUsed;

    //已过期
    private Integer expired;
}
