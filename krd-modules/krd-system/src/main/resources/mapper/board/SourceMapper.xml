<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.bullboard.mapper.SourceMapper">

    <select id="getSourceStatistics" resultType="com.ruoyi.bullboard.vo.SourceStatisticsVO">
        SELECT
            COALESCE(SUM(CASE WHEN type = 0 THEN point ELSE 0 END), 0) AS totalPoints,
            COALESCE(SUM(CASE WHEN type = 1 THEN point ELSE 0 END), 0) AS totalConsumed,
            COALESCE(SUM(CASE WHEN type = 0 THEN point ELSE 0 END), 0) AS totalRewarded,
            COALESCE(SUM(CASE WHEN type = 0 AND source = 1 THEN point ELSE 0 END), 0) AS totalSurvey,
            CASE 
                WHEN SUM(CASE WHEN type = 0 THEN point ELSE 0 END) > 0 THEN 
                    ROUND(SUM(CASE WHEN type = 1 THEN point ELSE 0 END) * 100.0 / SUM(CASE WHEN type = 0 THEN point ELSE 0 END), 2)
                ELSE 0.00 
            END AS usageRate
        FROM front_source
    </select>
</mapper>
