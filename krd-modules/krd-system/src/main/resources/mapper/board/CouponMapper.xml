<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.bullboard.mapper.CouponMapper">

    <select id="getCouponStatistics" resultType="com.ruoyi.bullboard.vo.CouponStatisticsVO">
        SELECT
            COALESCE(SUM(CASE WHEN fc.total = 0 THEN 999999 ELSE fc.total END), 0) AS totalIssued,
            COALESCE(COUNT(fci.id), 0) AS totalClaimed,
            COALESCE(SUM(CASE WHEN fci.status = '0' THEN 1 ELSE 0 END), 0) AS pendingUse,
            COALESCE(SUM(CASE WHEN fci.status = '1' THEN 1 ELSE 0 END), 0) AS totalUsed,
            CASE
                WHEN COUNT(fci.id) > 0 THEN
                    ROUND(SUM(CASE WHEN fci.status = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(fci.id), 2)
                ELSE 0.00
            END AS usageRate,
            CASE
                WHEN SUM(CASE WHEN fc.total = 0 THEN 999999 ELSE fc.total END) > 0 THEN
                    ROUND(COUNT(fci.id) * 100.0 / SUM(CASE WHEN fc.total = 0 THEN 999999 ELSE fc.total END), 2)
                ELSE 0.00
            END AS claimRate
        FROM front_coupon fc
        LEFT JOIN front_coupon_info fci ON fc.id = fci.coupon_id
        WHERE fc.is_del = 0
    </select>
    <select id="getCouponLine" resultType="com.ruoyi.bullboard.vo.CouponLineVo">


    </select>
</mapper>
